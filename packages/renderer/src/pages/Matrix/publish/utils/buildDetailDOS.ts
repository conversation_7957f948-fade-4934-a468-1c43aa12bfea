import { shuffle } from 'lodash'
import { Account, AccountProduct, DetailDOS, DetailDOProduct, Setting } from '@/types/matrix/douyin'
import { SimpleVideo } from '../components/VideoSelectionSection'

function getUrlSuffix(url?: string) {
  if (!url) return
  try {
    const parts = url.split('/')
    return parts.pop() 
  } catch {
    return undefined
  }
}

/**
 * 构建 detailDOS 数据
 * 根据发布设置、视频、标题、账号和商品信息进行排列组合
 * 关键特性：
 * - 不重复分配：在多视频模式下，每个视频只会分配给一个账号
 * - 随机组合：使用 lodash.shuffle 确保视频分配的随机性
 * - 标题优先级：视频自带标题 > 用户添加标题 > 默认标题
 */
export const buildDetailDOS = (
  accounts: Account[],
  videos: SimpleVideo[],
  titles: string[],
  accountProducts: AccountProduct[],
  setting: number,
  poiId: string,
  poiName: string
): DetailDOS[] => {
  const result: DetailDOS[] = []

  // 准备标题数组：优先使用视频自带标题，然后随机分配用户添加的标题
  const preparedTitles = [...titles] // 用户添加的标题
  const videoTitles: string[] = []

  // 为每个视频准备标题
  videos.forEach((video, index) => {
    if (video.name && video.name.trim()) {
      // 视频有自带标题，优先使用
      videoTitles[index] = video.name.trim()
    } else {
      // 视频没有标题，从用户添加的标题中随机选择（允许重复）
      if (preparedTitles.length > 0) {
        const randomIndex = Math.floor(Math.random() * preparedTitles.length)
        videoTitles[index] = preparedTitles[randomIndex]
      } else {
        // 如果没有可用标题，使用默认标题
        videoTitles[index] = `视频${index + 1}`
      }
    }
  })

  if (setting === Setting.ONE_ACCOUNT_ONE_VIDEO) {
    // 一个账号一个视频：账号和视频一一对应，都进行随机排序
    const shuffledAccounts = shuffle([...accounts])
    const shuffledVideos = shuffle([...videos])
    const minLength = Math.min(shuffledAccounts.length, shuffledVideos.length)

    for (let i = 0; i < minLength; i++) {
      const account = shuffledAccounts[i]
      const video = shuffledVideos[i]

      // 找到原始视频的索引以获取对应的标题
      const originalVideoIndex = videos.findIndex(v => v.url === video.url)

      // 获取该账号的商品信息
      const accountProduct = accountProducts.find(ap => ap.accountId === account.id)
      const products: DetailDOProduct[] = accountProduct?.products.map(p => ({
        title: p.title,
        url: p.url
      })) || []

      result.push({
        accountId: account.id.toString(),
        cover: getUrlSuffix(video?.cover) ?? '',
        description: '',
        poiId,
        poiName,
        products,
        title: videoTitles[originalVideoIndex] || video.name || `视频${originalVideoIndex + 1}`,
        url: video.url
      })
    }
  } else if (setting === Setting.ONE_ACCOUNT_MULTIPLE_VIDEOS) {
    // 一个账号多个视频：每个账号随机分配多个不重复的视频
    const shuffledVideos = shuffle([...videos])
    const videosPerAccount = Math.ceil(shuffledVideos.length / accounts.length)

    accounts.forEach((account, accountIndex) => {
      // 为每个账号分配视频
      const startIndex = accountIndex * videosPerAccount
      const endIndex = Math.min(startIndex + videosPerAccount, shuffledVideos.length)
      const accountVideos = shuffledVideos.slice(startIndex, endIndex)

      // 获取该账号的商品信息
      const accountProduct = accountProducts.find(ap => ap.accountId === account.id)
      const products: DetailDOProduct[] = accountProduct?.products.map(p => ({
        title: p.title,
        url: p.url
      })) || []

      accountVideos.forEach(video => {
        // 找到原始视频的索引以获取对应的标题
        const originalVideoIndex = videos.findIndex(v => v.url === video.url)

        result.push({
          accountId: account.id.toString(),
          cover: getUrlSuffix(video?.cover) ?? '',
          description: '',
          poiId,
          poiName,
          products,
          title: videoTitles[originalVideoIndex] || video.name || `视频${originalVideoIndex + 1}`,
          url: video.url
        })
      })
    })
  }

  return result
}
