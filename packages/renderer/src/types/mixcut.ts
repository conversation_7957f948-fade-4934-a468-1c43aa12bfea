
export namespace Mixcut {
  export type SavedMixcut = {
    id: number
    scriptId: number
    name: string
    cover: string
    duration: number
    repetitionRate: number
    product: number
    url: string
    projectId: string
    createAt: any
    updateAt: any
    createTime: number
  }

  export type RequestRender = {
    scriptId: number;
    name: string;
    coverObjectId: string
    duration: number;
    objectId: string;
    resolution: string;
    fps: number;

    bitRate: number;
    createAt: number;
    display: number;
    isSaveList: boolean;
    itemId: string;
    previewId: number;
    priority: number;
    product: number;
    repetitionRate: number;
    [property: string]: any;
  }

  export enum MixcutRenderTaskStatus {
    /**
     * 等待渲染
     */
    WAITING = 1,

    /**
     * 任务已分配
     */
    ASSIGNED = 2,

    /**
     * 正在渲染
     */
    RENDERING = 3,

    /**
     * 渲染完成
     */
    COMPLETED = 4,

    /**
     * 上传完成
     */
    UPLOADED = 5,

    /**
     * 渲染失败
     */
    FAILED = 6,

    /**
     * 任务取消
     */
    CANCELED = 7,

    /**
     * 任务分配失败
     */
    ASSIGN_FAILED = 21,

    /**
     * 封面上传完成
     */
    COVER_UPLOADED = 51
  }

  export type MixcutRenderTaskInfo = {
    id: number;
    taskNo: string;
    name: string;
    progress: number;
    projectId: number;
    status: MixcutRenderTaskStatus;
    errorMsg?: string;
  }
}
