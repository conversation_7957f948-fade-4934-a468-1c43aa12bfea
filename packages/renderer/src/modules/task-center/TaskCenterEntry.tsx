import React, { useMemo } from 'react'
import { Popover, PopoverTrigger } from '@/components/ui/popover'
import { Button } from '@/components/ui/button'
import { ArrowUpDown } from 'lucide-react'
import { PopoverContent } from '@radix-ui/react-popover'
import { TaskCenter } from './components/TaskCenter'
import { useTaskCenter } from './context/context'

export const TaskCenterEntry = () => {
  const { stats } = useTaskCenter().uploading

  const pendingCounts = useMemo(() => stats?.pending_count, [stats])

  return (
    <Popover>
      <PopoverTrigger asChild>
        <div className="relative">
          <Button size="icon" variant="ghost">
            <ArrowUpDown size={18} />
          </Button>
          {!!pendingCounts && (
            <div
              className="absolute -top-1 -right-1 bg-primary-accent font-bold text-xs rounded-full aspect-square w-5 flex items-center justify-center"
            >
              {pendingCounts}
            </div>
          )}
        </div>
      </PopoverTrigger>

      <PopoverContent className="w-auto p-0" align="end" side="bottom" sideOffset={8}>
        <TaskCenter />
      </PopoverContent>
    </Popover>
  )
}
