import React from 'react'
import { TaskCenterContext } from './context'
import { useUploadTasks } from './useUploadTasks'

export const UploadTasksProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const uploading = useUploadTasks()

  return (
    <TaskCenterContext.Provider
      value={{
        ...uploading,
        uploading
      }}
    >
      {children}
    </TaskCenterContext.Provider>
  )
}
