import { useUploadTasks } from './useUploadTasks'
import React from 'react'

export type TaskCenterValues = ReturnType<typeof useUploadTasks> & {
  uploading: ReturnType<typeof useUploadTasks>
}

export const TaskCenterContext = React.createContext<TaskCenterValues>({} as any)

export const useTaskCenter = () => {
  const ctx = React.useContext(TaskCenterContext)
  if (!ctx) {
    throw new Error('useUploadTasks must be used within an UploadTasksProvider')
  }
  return ctx
}
