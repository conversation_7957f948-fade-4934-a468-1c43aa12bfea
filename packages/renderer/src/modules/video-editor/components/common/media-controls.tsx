import React, { useCallback, useMemo } from 'react'
import { FormNumberInput, FormSlider, SectionTitle } from './form-components'
import { Overlay, OverlayType, Progressive } from '@clipnest/remotion-shared/types'
import { FPS } from '../../constants'
import _ from 'lodash'
import { useOverlayEditing } from '@/modules/video-editor/contexts'
import { updateProgressiveOverlayDuration } from '@/modules/video-editor/utils/overlay-helper'

type ProgressiveOverlay = Overlay & Progressive

/**
 * 媒体控制组件的 Props 接口
 */
interface MediaControlsProps {
  /**
   * 变速的最小值，默认为 0.25
   */
  minSpeed?: number

  /**
   * 变速的最大值，默认为 2
   */
  maxSpeed?: number

  /**
   * 变速的步长，默认为 0.25
   */
  speedStep?: number

  /**
   * 时长的最小值（秒），默认为 0.1
   */
  minDuration?: number

  /**
   * 时长的步长（秒），默认为 0.1
   */
  durationStep?: number

  /**
   * 是否显示变速控制，默认为 true
   */
  showSpeedControl?: boolean

  /**
   * 是否显示时长控制，默认为 true
   */
  showDurationControl?: boolean
}

const CutPanel: React.FC = () => {
  const frameToSecond = (frame = 0) =>  _.round((frame ?? 0) / FPS, 1)

  const { localOverlay, updateEditingOverlay } = useOverlayEditing<ProgressiveOverlay>()

  const { trimStartMax, trimEndMax } = useMemo(() => {
    const minDuration = FPS * 0.5
    return {
      trimStartMax: localOverlay.originalDurationFrames - minDuration - (localOverlay.trimEndFrames ?? 0),
      trimEndMax: localOverlay.originalDurationFrames - minDuration - (localOverlay.trimStartFrames ?? 0)
    }
  }, [localOverlay])

  const handleTrim = useCallback((props: { start?: number, end?: number }) => {
    return updateEditingOverlay(overlay => {
      const trimStartFrames = (props.start !== undefined ? props.start * FPS : undefined) ?? overlay.trimStartFrames ?? 0
      const trimEndFrames = (props.end !== undefined ? props.end * FPS : undefined) ?? overlay.trimEndFrames ?? 0

      return updateProgressiveOverlayDuration({
        ...overlay,
        trimStartFrames,
        trimEndFrames,
      })
    })
  }, [updateEditingOverlay])

  return (
    <div className="overlay-setting-card">
      <SectionTitle
        title={`${localOverlay.type === OverlayType.VIDEO ? '视频' : '音频'}时长裁剪`}
        onReset={() => {
          updateEditingOverlay(o => ({
            ...o,
            trimStartFrames: 0,
            trimEndFrames: 0,
            durationInFrames: o.originalDurationFrames * (o.speed ?? 1)
          }), true)
        }}
      />
      <div className="grid grid-cols-2 gap-3">
        <FormNumberInput
          label="去片头"
          suffix="秒"
          value={frameToSecond(localOverlay.trimStartFrames)}
          onChange={value => handleTrim({ start: value })}
          onBlur={() => updateEditingOverlay({}, true)}
          min={0}
          max={frameToSecond(trimStartMax)}
          step={0.1}
          decimalPlaces={1}
          formatDisplay={true}
        />
        <FormNumberInput
          label="去片尾"
          suffix="秒"
          value={frameToSecond(localOverlay.trimEndFrames)}
          onChange={value => handleTrim({ end: value })}
          onBlur={() => updateEditingOverlay({}, true)}
          min={0}
          max={frameToSecond(trimEndMax)}
          step={0.1}
          decimalPlaces={1}
          formatDisplay={true}
        />
      </div>

      {/* 显示裁剪后的时长 */}
      {/*{Boolean(localOverlay.trimStartFrames || localOverlay.trimEndFrames) && (*/}
      <div className="flex flex-col gap-3 px-4">
        <div className="flex justify-between text-xs text-muted-foreground rounded-md">
          <span>原始时长:</span>
          <span className="font-medium">
            {frameToSecond(localOverlay.originalDurationFrames)}秒
          </span>
        </div>
        <div className="flex justify-between text-xs text-muted-foreground rounded-md">
          <span>裁剪后时长(该时长不受变速影响):</span>
          <span className="font-medium">
            {frameToSecond(localOverlay.originalDurationFrames - (localOverlay.trimStartFrames ?? 0) - (localOverlay.trimEndFrames ?? 0))}秒
          </span>
        </div>
      </div>
      {/*)}*/}
    </div>
  )
}

/**
 * 变速控制组件
 *
 * 提供媒体变速调节功能，支持重置和实时数值显示
 *
 */
export function SpeedControl({
  minSpeed = 0.25,
  maxSpeed = 4,
  speedStep = 0.25
}: Pick<MediaControlsProps, 'minSpeed' | 'maxSpeed' | 'speedStep'>) {
  const { localOverlay, updateEditingOverlay } = useOverlayEditing<ProgressiveOverlay>()

  /**
   * 变速处理函数
   */
  const handleSpeedChange = (newSpeed: number, commit?: boolean) => {
    return updateEditingOverlay(
      () => updateProgressiveOverlayDuration({
        ...localOverlay,
        speed: newSpeed
      }),
      commit
    )
  }

  return (
    <div className="overlay-setting-card">
      <div className="flex items-center justify-between">
        <SectionTitle title={`变速 (${localOverlay?.speed?.toFixed(2) ?? 1}X)`} />
        <button
          onClick={() => handleSpeedChange(1, true)}
          className={`text-xs px-2.5 py-1.5 rounded-md transition-colors ${
            (localOverlay?.speed ?? 1) !== 1
              ? 'bg-primary/20 text-primary hover:bg-primary/30'
              : 'text-muted-foreground '
          }`}
        >
          重置
        </button>
      </div>
      <FormSlider
        showInput={false}
        min={minSpeed}
        max={maxSpeed}
        step={speedStep}
        value={localOverlay?.speed ?? 1}
        onChange={(val, commit) => handleSpeedChange(val, commit)}
      />
      <div className="flex justify-between text-xs text-muted-foreground">
        <span>{minSpeed}x</span>
        <span>{maxSpeed}x</span>
      </div>
    </div>
  )
}

/**
 * 时长控制组件
 *
 * 提供媒体时长调节功能，支持实时数值显示
 *
 * @deprecated
 */
export function DurationControl({
  minDuration = 0.1,
  durationStep = 0.1
}: Pick<MediaControlsProps, 'minDuration' | 'durationStep'>) {
  const { localOverlay, updateEditingOverlay } = useOverlayEditing<ProgressiveOverlay>()

  const maxDuration = localOverlay.originalDurationFrames / FPS

  return (
    <div className="overlay-setting-card">
      <div className="flex items-center justify-between">
        <SectionTitle title="时长" />
        <span className="text-xs font-medium text-muted-foreground px-2 py-1 rounded">
          {((localOverlay?.durationInFrames ?? 0) / FPS).toFixed(2)}秒
        </span>
      </div>
      <FormSlider
        showInput={false}
        min={minDuration}
        max={maxDuration}
        step={durationStep}
        value={localOverlay?.durationInFrames / FPS}
        onChange={(val, commit) => updateEditingOverlay({ durationInFrames: Math.round(val * FPS) }, commit)}
      />
    </div>
  )
}

/**
 * 媒体控制组件. 包含以下子功能:
 *   - 变速控制
 *   - 时长控制 (去片头/去片尾)
 *
 * @template T - 继承自 BaseOverlay 且具有 speed 属性的 overlay 类型
 * @param props - 组件属性
 * @returns 媒体控制 UI
 */
export function MediaControls({
  minSpeed = 0.25,
  maxSpeed = 4,
  speedStep = 0.25,
  minDuration = 0.1,
  durationStep = 0.1,
  showSpeedControl = true,
  showDurationControl = false
}: MediaControlsProps) {
  return (
    <>
      <CutPanel />

      {showSpeedControl && (
        <SpeedControl
          minSpeed={minSpeed}
          maxSpeed={maxSpeed}
          speedStep={speedStep}
        />
      )}

      {showDurationControl && (
        <DurationControl
          minDuration={minDuration}
          durationStep={durationStep}
        />
      )}
    </>
  )
}
